import { MembershipCardOrderStatus } from '@/constant';
import {
  adminCancel,
  adminDelete,
  adminRefund,
  cancel,
  index,
  pay,
  refund,
  remove,
} from '@/services/membership-card-orders';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Dropdown, message, Modal, Popconfirm, Space } from 'antd';
import type { MenuProps } from 'antd';
import { DownOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import moment from 'moment';
import React, { useRef, useState } from 'react';
import AdminActionModal from '../../../Appointment/AdminActionModal';
import OrderDetailDrawer from './components/OrderDetailDrawer';

/**
 * 权益卡订单管理组件
 */
const Order: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const actionRef = useRef<ActionType>();
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [currentOrder, setCurrentOrder] = useState<
    API.MembershipCardOrder | undefined
  >(undefined);

  // 管理员操作弹窗状态
  const [adminActionVisible, setAdminActionVisible] = useState<boolean>(false);
  const [adminActionType, setAdminActionType] = useState<'cancel' | 'refund' | 'delete'>('cancel');
  const [adminActionLoading, setAdminActionLoading] = useState<boolean>(false);

  // 处理查看详情
  const handleViewDetail = (record: API.MembershipCardOrder) => {
    setCurrentOrder(record);
    setDetailVisible(true);
  };

  // 处理支付订单
  const handlePay = async (id: number) => {
    try {
      const response = await pay(id);
      if (response.errCode) {
        message.error(response.msg || '支付失败');
      } else {
        message.success('支付成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('支付失败', error);
      message.error('支付失败，请重试');
    }
  };

  // 处理取消订单
  const handleCancel = async (id: number) => {
    try {
      const response = await cancel(id);
      if (response.errCode) {
        message.error(response.msg || '取消失败');
      } else {
        message.success('取消成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('取消失败', error);
      message.error('取消失败，请重试');
    }
  };

  // 处理退款
  const handleRefund = async (id: number) => {
    try {
      const response = await refund(id);
      if (response.errCode) {
        message.error(response.msg || '退款失败');
      } else {
        message.success('退款成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('退款失败', error);
      message.error('退款失败，请重试');
    }
  };

  // 处理删除订单
  const handleDelete = async (id: number) => {
    try {
      const response = await remove(id);
      if (response.errCode) {
        message.error(response.msg || '删除失败');
      } else {
        message.success('删除成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('删除失败', error);
      message.error('删除失败，请重试');
    }
  };

  /** 管理员操作 - 打开确认弹窗 */
  const handleAdminAction = (record: API.MembershipCardOrder, actionType: 'cancel' | 'refund' | 'delete') => {
    setCurrentOrder(record);
    setAdminActionType(actionType);
    setAdminActionVisible(true);
  };

  /** 管理员操作 - 确认执行 */
  const handleAdminActionConfirm = async (reason?: string) => {
    if (!currentOrder || !initialState?.id) {
      message.error('操作失败：缺少必要信息');
      return;
    }

    setAdminActionLoading(true);
    try {
      const operatorId = initialState.id;
      let response;

      switch (adminActionType) {
        case 'cancel':
          response = await adminCancel(currentOrder.id, { operatorId, reason });
          break;
        case 'refund':
          response = await adminRefund(currentOrder.id, { operatorId, reason });
          break;
        case 'delete':
          response = await adminDelete(currentOrder.id, { operatorId, reason });
          break;
        default:
          message.error('未知操作类型');
          return;
      }

      if (response.errCode) {
        message.error(response.msg);
      } else {
        const actionText = adminActionType === 'cancel' ? '取消' :
                          adminActionType === 'refund' ? '申请退款' : '删除';
        message.success(`${actionText}成功`);
        actionRef?.current?.reload();
        setAdminActionVisible(false);
      }
    } catch (error) {
      console.error('管理员操作失败:', error);
      message.error('操作失败，请重试');
    } finally {
      setAdminActionLoading(false);
    }
  };

  /** 管理员操作 - 取消弹窗 */
  const handleAdminActionCancel = () => {
    setAdminActionVisible(false);
    setCurrentOrder(undefined);
  };

  /** 生成财务操作下拉菜单 */
  const getFinanceMenuItems = (record: API.MembershipCardOrder): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    // 支付
    if (record.status === MembershipCardOrderStatus.待付款) {
      items.push({
        key: 'pay',
        label: '支付',
        onClick: () => handlePay(record.id),
      });
    }

    // 取消
    if (record.status === MembershipCardOrderStatus.待付款) {
      items.push({
        key: 'cancel',
        label: <span style={{ color: '#ff4d4f' }}>取消</span>,
        onClick: () => {
          Modal.confirm({
            title: '确定要取消此订单吗？',
            icon: <ExclamationCircleOutlined />,
            onOk() {
              handleCancel(record.id);
            },
          });
        },
      });
    }

    // 退款
    if (record.status === MembershipCardOrderStatus.已付款) {
      items.push({
        key: 'refund',
        label: <span style={{ color: '#ff4d4f' }}>退款</span>,
        onClick: () => {
          Modal.confirm({
            title: '确定要退款此订单吗？',
            icon: <ExclamationCircleOutlined />,
            onOk() {
              handleRefund(record.id);
            },
          });
        },
      });
    }

    // 删除
    if (record.status === MembershipCardOrderStatus.已取消 || record.status === MembershipCardOrderStatus.已退款) {
      items.push({
        key: 'delete',
        label: <span style={{ color: '#ff4d4f' }}>删除</span>,
        onClick: () => {
          Modal.confirm({
            title: '确定要删除此订单吗？',
            icon: <ExclamationCircleOutlined />,
            onOk() {
              handleDelete(record.id);
            },
          });
        },
      });
    }

    return items;
  };

  /** 生成管理员操作下拉菜单 */
  const getAdminMenuItems = (record: API.MembershipCardOrder): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    // 管理员取消
    if (![MembershipCardOrderStatus.已取消, MembershipCardOrderStatus.已退款].includes(record.status)) {
      items.push({
        key: 'admin-cancel',
        label: '管理员取消',
        onClick: () => handleAdminAction(record, 'cancel'),
        style: { color: '#ff7a45' },
      });
    }

    // 管理员退款
    if (record.status !== MembershipCardOrderStatus.已退款) {
      items.push({
        key: 'admin-refund',
        label: '管理员退款',
        onClick: () => handleAdminAction(record, 'refund'),
        style: { color: '#ff4d4f' },
      });
    }

    // 管理员删除
    items.push({
      key: 'admin-delete',
      label: '管理员删除',
      onClick: () => handleAdminAction(record, 'delete'),
      style: { color: '#cf1322' },
    });

    return items;
  };

  // 表格列定义
  const columns: ProColumns<API.MembershipCardOrder>[] = [
    {
      title: '订单ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '订单编号',
      dataIndex: 'sn',
      width: 180,
      copyable: true,
    },
    {
      title: '用户',
      dataIndex: ['customer', 'nickname'],
      width: 120,
      ellipsis: true,
      render: (_, record) =>
        record.customer?.nickname || `用户${record.customerId}`,
    },
    {
      title: '用户手机',
      dataIndex: ['customer', 'phone'],
      width: 120,
    },
    {
      title: '权益卡',
      dataIndex: ['cardType', 'name'],
      width: 150,
      ellipsis: true,
    },
    {
      title: '卡类型',
      dataIndex: ['cardType', 'type'],
      width: 100,
      valueEnum: {
        discount: { text: '折扣卡', status: 'Processing' },
        times: { text: '次卡', status: 'Success' },
      },
    },
    {
      title: '订单金额',
      dataIndex: 'amount',
      width: 100,
      search: false,
      render: (_, record) => `¥${record.amount}`,
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        [MembershipCardOrderStatus.待付款]: {
          text: '待付款',
          status: 'Warning',
        },
        [MembershipCardOrderStatus.已付款]: {
          text: '已支付',
          status: 'Success',
        },
        [MembershipCardOrderStatus.已取消]: {
          text: '已取消',
          status: 'Default',
        },
        [MembershipCardOrderStatus.已退款]: { text: '已退款', status: 'Error' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      valueType: 'dateTime',
      sorter: true,
      search: false,
      render: (_, record) =>
        record.createdAt
          ? moment(record.createdAt).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '支付时间',
      dataIndex: 'payTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
      render: (_, record) =>
        record.payTime
          ? moment(record.payTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      render: (_, record) => {
        const financeMenuItems = getFinanceMenuItems(record);
        const adminMenuItems = getAdminMenuItems(record);

        return (
          <Space>
            {/* 基础操作 - 始终显示 */}
            <Button type="link" onClick={() => handleViewDetail(record)}>
              详情
            </Button>

            {/* 更多操作下拉菜单 - 分组显示 */}
            <Dropdown
              menu={{
                items: [
                  // 财务操作组
                  ...(financeMenuItems && financeMenuItems.length > 0 ? [
                    {
                      type: 'group',
                      label: '财务操作',
                      children: financeMenuItems,
                    },
                  ] : []),

                  // 管理员操作组
                  ...(adminMenuItems && adminMenuItems.length > 0 ? [
                    ...(financeMenuItems && financeMenuItems.length > 0 ? [{ type: 'divider' }] : []),
                    {
                      type: 'group',
                      label: '管理员操作',
                      children: adminMenuItems,
                    },
                  ] : []),
                ].filter(Boolean) as MenuProps['items']
              }}
              trigger={['click']}
            >
              <Button type="link">
                更多 <DownOutlined />
              </Button>
            </Dropdown>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable<API.MembershipCardOrder>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1500 }}
        request={async (params) => {
          const response = await index({
            ...params,
          });

          if (response.errCode) {
            message.error(response.msg || '获取权益卡订单列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
      />

      {/* 订单详情抽屉 */}
      <OrderDetailDrawer
        visible={detailVisible}
        order={currentOrder}
        onClose={() => {
          setDetailVisible(false);
          setCurrentOrder(undefined);
        }}
      />

      {/* 管理员操作弹窗 */}
      <AdminActionModal
        open={adminActionVisible}
        onCancel={handleAdminActionCancel}
        onConfirm={handleAdminActionConfirm}
        title={`管理员${adminActionType === 'cancel' ? '取消' : adminActionType === 'refund' ? '申请退款' : '删除'}权益卡订单`}
        content={`确定要${adminActionType === 'cancel' ? '取消' : adminActionType === 'refund' ? '申请退款' : '删除'}权益卡订单 ${currentOrder?.sn} 吗？`}
        actionType={adminActionType}
        loading={adminActionLoading}
      />
    </>
  );
};

export default Order;
