# 管理员订单操作功能说明

## 功能概述

为了给管理员提供更大的权限，新增了三个管理员专用的订单操作功能，允许管理员对任何状态的订单进行操作，绕过常规的状态限制。

## 新增功能

### 1. 管理员取消订单
- **功能描述**：管理员可以取消任何状态的订单（除了已取消和已退款的订单）
- **按钮位置**：订单列表操作列中的"管理员取消"按钮
- **显示条件**：订单状态不是"已取消"或"已退款"
- **操作流程**：
  1. 点击"管理员取消"按钮
  2. 在弹窗中填写取消原因（可选）
  3. 确认操作
  4. 系统自动处理卡券退回并记录操作日志

### 2. 管理员申请退款
- **功能描述**：管理员可以对任何状态的订单申请退款（除了已在退款流程中的订单）
- **按钮位置**：订单列表操作列中的"管理员退款"按钮
- **显示条件**：订单状态不是"退款中"或"已退款"
- **操作流程**：
  1. 点击"管理员退款"按钮
  2. 在弹窗中填写退款原因（可选）
  3. 确认操作
  4. 订单状态变为"退款中"，后续仍需要通过审核退款接口进行审核

### 3. 管理员删除订单
- **功能描述**：管理员可以删除任何状态的订单
- **按钮位置**：订单列表操作列中的"管理员删除"按钮
- **显示条件**：始终显示
- **操作流程**：
  1. 点击"管理员删除"按钮
  2. 在弹窗中填写删除原因（必填）
  3. 确认操作
  4. 订单被永久删除，无法恢复

## 安全特性

### 1. 二次确认机制
- 所有管理员操作都需要通过确认弹窗进行二次确认
- 删除操作必须填写原因，其他操作原因可选
- 弹窗中会显示警告信息，提醒操作的后果

### 2. 操作日志记录
- 所有操作都会记录详细的操作日志
- 包含操作员ID、操作时间、操作原因等信息
- 便于后续审计和追踪

### 3. 权限控制
- 只有管理员角色才能看到和使用这些功能
- 操作时会自动获取当前登录用户的ID作为操作员ID

## 与现有功能的区别

| 功能 | 现有接口 | 管理员接口 | 主要区别 |
|------|----------|------------|----------|
| 取消订单 | 只能取消待付款订单 | 可取消任何状态订单 | 无状态限制 |
| 申请退款 | 只能对待接单订单申请 | 可对任何状态订单申请 | 无状态限制 |
| 删除订单 | 只能删除已取消订单 | 可删除任何状态订单 | 无状态限制 |
| 操作记录 | 基础日志 | 详细操作日志 | 包含操作员和原因 |

## 使用建议

1. **谨慎使用**：这些功能权限很高，建议只在必要时使用
2. **填写原因**：建议在操作时填写详细的原因说明，便于后续审计
3. **删除操作**：删除操作不可逆，请特别谨慎
4. **定期检查**：建议定期检查操作日志，确保权限使用合规

## 技术实现

### 新增接口
- `POST /admin/orders/{orderId}/admin-cancel` - 管理员取消订单
- `POST /admin/orders/sn/{sn}/admin-apply-refund` - 管理员申请退款
- `DELETE /admin/orders/{orderId}/admin-delete` - 管理员删除订单

### 新增组件
- `AdminActionModal.tsx` - 管理员操作确认弹窗组件

### 修改文件
- `src/services/order.ts` - 添加管理员专用接口
- `src/pages/Appointment/index.tsx` - 添加管理员操作按钮和处理逻辑
