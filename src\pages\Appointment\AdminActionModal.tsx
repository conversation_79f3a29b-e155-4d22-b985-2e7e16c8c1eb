import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Form, Input, Modal, Typography } from 'antd';
import React from 'react';

const { Text } = Typography;

interface AdminActionModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (reason?: string) => void;
  title: string;
  content: string;
  actionType: 'cancel' | 'refund' | 'delete';
  loading?: boolean;
}

const AdminActionModal: React.FC<AdminActionModalProps> = ({
  open,
  onCancel,
  onConfirm,
  title,
  content,
  actionType,
  loading = false,
}) => {
  const [form] = Form.useForm();

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onConfirm(values.reason);
    } catch (error) {
      // 表单验证失败
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const getActionColor = () => {
    switch (actionType) {
      case 'cancel':
        return '#ff7a45';
      case 'refund':
        return '#ff4d4f';
      case 'delete':
        return '#cf1322';
      default:
        return '#ff4d4f';
    }
  };

  const getWarningText = () => {
    switch (actionType) {
      case 'cancel':
        return '此操作将取消订单，取消后订单状态将变为"已取消"，相关卡券将自动退回。';
      case 'refund':
        return '此操作将申请退款，申请后订单状态将变为"退款中"，后续仍需要通过审核退款接口进行审核。';
      case 'delete':
        return '此操作将永久删除订单，删除后无法恢复，请谨慎操作！';
      default:
        return '';
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ExclamationCircleOutlined
            style={{ color: getActionColor(), marginRight: 8 }}
          />
          {title}
        </div>
      }
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      okText="确认"
      cancelText="取消"
      okButtonProps={{
        danger: true,
      }}
      width={500}
    >
      <div style={{ marginBottom: 16 }}>
        <Text>{content}</Text>
      </div>
      
      <div style={{ 
        padding: '12px 16px', 
        backgroundColor: '#fff2f0', 
        border: '1px solid #ffccc7',
        borderRadius: '6px',
        marginBottom: 16
      }}>
        <Text type="warning" style={{ fontSize: '14px' }}>
          <ExclamationCircleOutlined style={{ marginRight: 4 }} />
          {getWarningText()}
        </Text>
      </div>

      <Form form={form} layout="vertical">
        <Form.Item
          name="reason"
          label="操作原因"
          rules={[
            {
              required: actionType === 'delete',
              message: '删除操作必须填写原因',
            },
          ]}
        >
          <Input.TextArea
            rows={3}
            placeholder={`请输入${actionType === 'cancel' ? '取消' : actionType === 'refund' ? '退款' : '删除'}原因...`}
            maxLength={200}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AdminActionModal;
